import { RpaStep } from '../../types';
import { ValidationResult, ValidationError } from '../../utils';
import type {
  FortnoxCreateVoucherStep,
  FortnoxUploadFileStep,
  FortnoxAttachFileToVoucherStep,
  FortnoxUploadAndCreateVoucherStep
} from '../../types/steps/api';

/**
 * Validerar Fortnox Create Voucher steg
 */
export function validateFortnoxCreateVoucherStep(step: FortnoxCreateVoucherStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'fortnoxCreateVoucher') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "fortnoxCreateVoucher"',

    });
  }



  // Validera input-variabel
  if (!step.inputVariable?.trim()) {
    errors.push({
      field: 'inputVariable',
      message: 'Input-variabel är obligatorisk',

    });
  }

  // Validera AI-prompt
  if (!step.aiPrompt?.trim()) {
    errors.push({
      field: 'aiPrompt',
      message: 'AI-prompt är obligatorisk',

    });
  }

  // Validera verifikationsserie
  if (step.voucherSeries && !/^[A-Z]$/.test(step.voucherSeries)) {
    errors.push({
      field: 'voucherSeries',
      message: 'Verifikationsserie måste vara en stor bokstav (A-Z)',

    });
  }

  // Validera transaktionsdatum format (om angivet)
  if (step.transactionDate && !isValidDateFormat(step.transactionDate)) {
    errors.push({
      field: 'transactionDate',
      message: 'Transaktionsdatum måste vara i format YYYY-MM-DD eller en variabel',

    });
  }

  // Validera variabelnamn
  if (step.variableName && !/^[a-zA-Z][a-zA-Z0-9_-]*$/.test(step.variableName)) {
    errors.push({
      field: 'variableName',
      message: 'Variabelnamn måste börja med en bokstav och får endast innehålla bokstäver, siffror, bindestreck och understreck',

    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}



/**
 * Kontrollerar om datum är i giltigt format (YYYY-MM-DD eller variabel)
 */
function isValidDateFormat(date: string): boolean {
  // Tillåt variabler (börjar med ${)
  if (date.startsWith('${') && date.endsWith('}')) {
    return true;
  }

  // Kontrollera YYYY-MM-DD format
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(date)) {
    return false;
  }

  // Kontrollera att det är ett giltigt datum
  const parsedDate = new Date(date);
  return parsedDate instanceof Date && !isNaN(parsedDate.getTime());
}

/**
 * Validerar Fortnox Upload File steg
 */
export function validateFortnoxUploadFileStep(step: FortnoxUploadFileStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'fortnoxUploadFile') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "fortnoxUploadFile"',
    });
  }

  // Validera input-variabel
  if (!step.inputVariable?.trim()) {
    errors.push({
      field: 'inputVariable',
      message: 'Input-variabel är obligatorisk',
    });
  }

  // Validera variabelnamn
  if (step.variableName && !/^[a-zA-Z][a-zA-Z0-9_-]*$/.test(step.variableName)) {
    errors.push({
      field: 'variableName',
      message: 'Variabelnamn måste börja med en bokstav och får endast innehålla bokstäver, siffror, bindestreck och understreck',
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validerar Fortnox Attach File to Voucher steg
 */
export function validateFortnoxAttachFileToVoucherStep(step: FortnoxAttachFileToVoucherStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'fortnoxAttachFileToVoucher') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "fortnoxAttachFileToVoucher"',
    });
  }

  // Validera fil-ID variabel
  if (!step.fileIdVariable?.trim()) {
    errors.push({
      field: 'fileIdVariable',
      message: 'Fil-ID variabel är obligatorisk',
    });
  }

  // Validera verifikationsnummer variabel
  if (!step.voucherNumberVariable?.trim()) {
    errors.push({
      field: 'voucherNumberVariable',
      message: 'Verifikationsnummer variabel är obligatorisk',
    });
  }

  // Validera variabelnamn
  if (step.variableName && !/^[a-zA-Z][a-zA-Z0-9_-]*$/.test(step.variableName)) {
    errors.push({
      field: 'variableName',
      message: 'Variabelnamn måste börja med en bokstav och får endast innehålla bokstäver, siffror, bindestreck och understreck',
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validerar Fortnox Upload and Create Voucher steg
 */
export function validateFortnoxUploadAndCreateVoucherStep(step: FortnoxUploadAndCreateVoucherStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'fortnoxUploadAndCreateVoucher') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "fortnoxUploadAndCreateVoucher"',
    });
  }

  // Validera fil input-variabel
  if (!step.fileInputVariable?.trim()) {
    errors.push({
      field: 'fileInputVariable',
      message: 'Fil input-variabel är obligatorisk',
    });
  }

  // Validera verifikation input-variabel
  if (!step.voucherInputVariable?.trim()) {
    errors.push({
      field: 'voucherInputVariable',
      message: 'Verifikation input-variabel är obligatorisk',
    });
  }

  // Validera AI-prompt
  if (!step.aiPrompt?.trim()) {
    errors.push({
      field: 'aiPrompt',
      message: 'AI-prompt är obligatorisk',
    });
  }

  // Validera verifikationsserie
  if (step.voucherSeries && !/^[A-Z]$/.test(step.voucherSeries)) {
    errors.push({
      field: 'voucherSeries',
      message: 'Verifikationsserie måste vara en stor bokstav (A-Z)',
    });
  }

  // Validera transaktionsdatum format (om angivet)
  if (step.transactionDate && !isValidDateFormat(step.transactionDate)) {
    errors.push({
      field: 'transactionDate',
      message: 'Transaktionsdatum måste vara i format YYYY-MM-DD eller en variabel',
    });
  }

  // Validera variabelnamn
  if (step.variableName && !/^[a-zA-Z][a-zA-Z0-9_-]*$/.test(step.variableName)) {
    errors.push({
      field: 'variableName',
      message: 'Variabelnamn måste börja med en bokstav och får endast innehålla bokstäver, siffror, bindestreck och understreck',
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Skapar ett nytt Fortnox Create Voucher steg med standardvärden
 */
export function createFortnoxCreateVoucherStep(): FortnoxCreateVoucherStep {
  return {
    id: '',
    type: 'fortnoxCreateVoucher',
    name: 'Skapa Fortnox Verifikation',
    description: 'Skapa verifikation i Fortnox med AI',
    inputVariable: '',
    aiPrompt: 'Skapa en balanserad verifikation baserat på input-data enligt svensk bokföringssed',
    voucherSeries: 'A',
    variableName: 'var-fortnox-voucher'
  };
}

/**
 * Skapar ett nytt Fortnox Upload File steg med standardvärden
 */
export function createFortnoxUploadFileStep(): FortnoxUploadFileStep {
  return {
    id: '',
    type: 'fortnoxUploadFile',
    name: 'Ladda upp fil till Fortnox',
    description: 'Ladda upp fil till Fortnox arkiv',
    inputVariable: '',
    variableName: 'var-fortnox-file'
  };
}

/**
 * Skapar ett nytt Fortnox Attach File to Voucher steg med standardvärden
 */
export function createFortnoxAttachFileToVoucherStep(): FortnoxAttachFileToVoucherStep {
  return {
    id: '',
    type: 'fortnoxAttachFileToVoucher',
    name: 'Koppla fil till verifikation',
    description: 'Koppla en uppladdad fil till en verifikation',
    fileIdVariable: '',
    voucherNumberVariable: '',
    variableName: 'var-fortnox-attachment'
  };
}

/**
 * Skapar ett nytt Fortnox Upload and Create Voucher steg med standardvärden
 */
export function createFortnoxUploadAndCreateVoucherStep(): FortnoxUploadAndCreateVoucherStep {
  return {
    id: '',
    type: 'fortnoxUploadAndCreateVoucher',
    name: 'Ladda upp fil och skapa verifikation',
    description: 'Ladda upp fil och skapa verifikation med automatisk koppling',
    fileInputVariable: '',
    voucherInputVariable: '',
    aiPrompt: 'Skapa en balanserad verifikation baserat på input-data enligt svensk bokföringssed',
    voucherSeries: 'A',
    variableName: 'var-fortnox-voucher-with-file'
  };
}

// Huvudvalidator för API-steg
export function validateApiStep(step: RpaStep): ValidationResult {
  switch ((step as any).type) {
    case 'fortnoxCreateVoucher':
      return validateFortnoxCreateVoucherStep(step as unknown as FortnoxCreateVoucherStep);
    case 'fortnoxUploadFile':
      return validateFortnoxUploadFileStep(step as unknown as FortnoxUploadFileStep);
    case 'fortnoxAttachFileToVoucher':
      return validateFortnoxAttachFileToVoucherStep(step as unknown as FortnoxAttachFileToVoucherStep);
    case 'fortnoxUploadAndCreateVoucher':
      return validateFortnoxUploadAndCreateVoucherStep(step as unknown as FortnoxUploadAndCreateVoucherStep);
    default:
      return {
        isValid: false,
        errors: [{
          field: 'type',
          message: `Okänd API steg-typ: ${step.type}`,

        }]
      };
  }
}

export function createApiStepFromType(stepType: string): any {
  switch (stepType) {
    case 'fortnoxCreateVoucher':
      return createFortnoxCreateVoucherStep();
    case 'fortnoxUploadFile':
      return createFortnoxUploadFileStep();
    case 'fortnoxAttachFileToVoucher':
      return createFortnoxAttachFileToVoucherStep();
    case 'fortnoxUploadAndCreateVoucher':
      return createFortnoxUploadAndCreateVoucherStep();
    default:
      throw new Error(`Okänd API steg-typ: ${stepType}`);
  }
}
