# Guide: <PERSON><PERSON>gg<PERSON> till Nya RPA-steg

Denna guide visar hur du lägger till ett nytt RPA-steg i systemet. Processen är designad för att vara så enkel som möjligt och tar cirka 30 minuter.

## Översikt

För att lägga till ett nytt steg behöver du:

1. **Definiera step-typ** i shared package
2. **Skapa validator** för step-data
3. **Implementera runner-logik** för att utföra steget
4. **Skapa editor-komponent** för UI
5. **Lägga till i toolbar** för att göra steget tillgängligt

## Steg-för-steg Guide

### 1. Definiera Step-typ

Först bestäm vilken kategori ditt steg tillhör:

- **navigation** - Navigering (navigate, goBack, etc.)
- **interaction** - Interaktion (click, fill, etc.)
- **waiting** - V<PERSON>nta<PERSON> (waitForSelector, etc.)
- **extraction** - Extraktion (extractText, etc.)
- **ai** - AI-processing (extractPdfText, etc.)
- **api** - API-anrop (framtida)

Skapa step-typen i `shared/src/types/steps/{kategori}.ts`:

```typescript
// shared/src/types/steps/interaction.ts
export interface MyNewStep extends RpaStepBase {
  type: 'myNewStep';
  selector: string;
  value?: string;
  timeout?: number;
  waitForElement?: boolean;
}
```

Lägg till i union type i `shared/src/types/steps/index.ts`:

```typescript
export type RpaStep = 
  | NavigateStep
  | ClickStep
  | MyNewStep  // Lägg till här
  // ... andra steg
```

### 2. Skapa Validator

Lägg till validator i `shared/src/validators/steps/{kategori}.ts`:

```typescript
// shared/src/validators/steps/interaction.ts
export function validateMyNewStep(step: MyNewStep): string[] {
  const errors: string[] = [];

  if (!step.selector?.trim()) {
    errors.push('Selector är obligatorisk');
  }

  if (step.timeout !== undefined && (step.timeout < 0 || step.timeout > 60000)) {
    errors.push('Timeout måste vara mellan 0 och 60000ms');
  }

  return errors;
}

export function createMyNewStep(): MyNewStep {
  return {
    id: '',
    type: 'myNewStep',
    name: 'Mitt nya steg',
    description: '',
    selector: '',
    timeout: 5000,
    waitForElement: true,
  };
}
```

Uppdatera huvudvalidatorn i `shared/src/validators/steps/index.ts`:

```typescript
export function validateStep(step: RpaStep): string[] {
  switch (step.type) {
    case 'myNewStep':
      return validateMyNewStep(step as MyNewStep);
    // ... andra cases
  }
}

export function createStepFromType(type: string): RpaStep {
  switch (type) {
    case 'myNewStep':
      return createMyNewStep();
    // ... andra cases
  }
}
```

### 3. Implementera Runner-logik

Lägg till metod i lämplig runner (oftast PlaywrightRunner):

```typescript
// backend/src/runners/playwright/PlaywrightRunner.ts
async executeMyNewStep(step: MyNewStep, context: ExecutionContext): Promise<void> {
  try {
    this.logInfo(`Utför myNewStep: ${step.name}`);

    if (!this.page) {
      throw new Error('Browser page är inte tillgänglig');
    }

    // Vänta på element om specificerat
    if (step.waitForElement) {
      await this.page.waitForSelector(step.selector, { 
        timeout: step.timeout || 5000 
      });
    }

    // Utför step-specifik logik
    if (step.value) {
      await this.page.fill(step.selector, step.value);
    } else {
      await this.page.click(step.selector);
    }

    // Lägg till random delay
    await this.randomDelay();

    this.logInfo('myNewStep slutfört framgångsrikt');

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Okänt fel';
    this.logError(`Fel vid myNewStep: ${errorMessage}`);
    throw error;
  }
}
```

Lägg till i executeStep switch:

```typescript
// backend/src/runners/playwright/PlaywrightRunner.ts
async executeStep(step: RpaStep, context: ExecutionContext): Promise<void> {
  switch (step.type) {
    case 'myNewStep':
      await this.executeMyNewStep(step as MyNewStep, context);
      break;
    // ... andra cases
  }
}
```

### 4. Skapa Editor-komponent

Skapa editor i `frontend/src/components/flow-editor/step-editors/{kategori}/`:

```typescript
// frontend/src/components/flow-editor/step-editors/interaction/MyNewStepEditor.tsx
import React from 'react';
import { MyNewStep } from '../../../../../shared/src/types/steps/interaction';
import { BaseStepEditorProps } from '../base/BaseStepEditor';

interface MyNewStepEditorProps extends BaseStepEditorProps {
  step: MyNewStep;
}

export const MyNewStepEditor: React.FC<MyNewStepEditorProps> = ({
  step,
  onStepChange,
  variables = []
}) => {
  const handleChange = (field: keyof MyNewStep, value: any) => {
    onStepChange({
      ...step,
      [field]: value
    });
  };

  return (
    <div className="step-editor">
      <div className="form-row">
        <div className="form-group">
          <label>Namn</label>
          <input
            type="text"
            value={step.name || ''}
            onChange={(e) => handleChange('name', e.target.value)}
            className="form-input"
          />
        </div>
      </div>

      <div className="form-row">
        <div className="form-group">
          <label>CSS Selector</label>
          <input
            type="text"
            value={step.selector || ''}
            onChange={(e) => handleChange('selector', e.target.value)}
            placeholder="CSS selector för element"
            className="form-input"
          />
        </div>
      </div>

      <div className="form-row">
        <div className="form-group">
          <label>Värde (valfritt)</label>
          <input
            type="text"
            value={step.value || ''}
            onChange={(e) => handleChange('value', e.target.value)}
            placeholder="Värde att fylla i"
            className="form-input"
          />
        </div>
      </div>

      <div className="form-row">
        <div className="form-group">
          <label>Timeout (ms)</label>
          <input
            type="number"
            value={step.timeout || 5000}
            onChange={(e) => handleChange('timeout', parseInt(e.target.value))}
            min="0"
            max="60000"
            className="form-input"
          />
        </div>
        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={step.waitForElement || false}
              onChange={(e) => handleChange('waitForElement', e.target.checked)}
            />
            Vänta på element
          </label>
        </div>
      </div>
    </div>
  );
};
```

Registrera editor i `frontend/src/components/flow-editor/step-editors/StepEditorRegistry.tsx`:

```typescript
case 'myNewStep':
  return <MyNewStepEditor {...props} step={step as MyNewStep} />;
```

### 5. Lägga till i Toolbar

Skapa step-definition i `frontend/src/components/flow-editor/step-definitions/{kategori}.ts`:

```typescript
// frontend/src/components/flow-editor/step-definitions/interaction.ts
export const myNewStepDefinition: StepDefinition = {
  type: 'myNewStep',
  name: 'Mitt Nya Steg',
  description: 'Beskrivning av vad steget gör',
  category: 'interaction',
  icon: 'MousePointer',
  color: '#fd746c',
  
  defaultStep: {
    id: '',
    type: 'myNewStep',
    name: 'Mitt Nya Steg',
    description: '',
    selector: '',
    timeout: 5000,
    waitForElement: true,
  },
  
  isValid: (step: any) => {
    return !!(step.name?.trim() && step.selector?.trim());
  },
  
  tooltip: 'Utför anpassad åtgärd på element',
  requiredRunner: 'playwright',
  createsVariables: [],
  requiresVariables: [],
  
  examples: [
    {
      name: 'Klicka på knapp',
      description: 'Klicka på en specifik knapp',
      step: {
        id: 'example-1',
        type: 'myNewStep',
        name: 'Klicka på Spara-knapp',
        selector: 'button[type="submit"]',
        timeout: 5000,
        waitForElement: true,
      }
    }
  ]
};
```

Lägg till i huvudlistan i `frontend/src/components/flow-editor/step-definitions/index.ts`:

```typescript
import { myNewStepDefinition } from './interaction';

export const stepDefinitions: StepDefinition[] = [
  // ... befintliga definitioner
  myNewStepDefinition,
];
```

## Automatiserad Generering

Du kan använda step-generator scriptet för att automatisera processen:

```bash
node tools/generate-step.js --type=myNewStep --category=interaction --runner=playwright
```

Detta skapar alla nödvändiga filer från templates.

## Testning

### 1. Kompilering

Kontrollera att allt kompilerar:

```bash
npm run build
```

### 2. Manuell testning

1. Starta utvecklingsservern
2. Skapa nytt flöde
3. Lägg till ditt nya steg från toolbar
4. Konfigurera steget
5. Kör flödet

### 3. Enhetstester

Skapa tester för validator:

```typescript
// shared/src/validators/steps/__tests__/interaction.test.ts
describe('validateMyNewStep', () => {
  test('ska validera giltigt steg', () => {
    const step: MyNewStep = {
      id: 'test',
      type: 'myNewStep',
      name: 'Test',
      selector: '.test-selector',
    };

    const errors = validateMyNewStep(step);
    expect(errors).toHaveLength(0);
  });

  test('ska kräva selector', () => {
    const step: MyNewStep = {
      id: 'test',
      type: 'myNewStep',
      name: 'Test',
      selector: '',
    };

    const errors = validateMyNewStep(step);
    expect(errors).toContain('Selector är obligatorisk');
  });
});
```

## Variabelhantering

Om ditt steg skapar variabler som andra steg kan använda, följ dessa riktlinjer:

### 1. Definiera variableName i step-typ

```typescript
export interface MyExtractStep extends RpaStepBase {
  type: 'myExtract';
  selector: string;
  variableName?: string; // Valfritt - använder default om ej angivet
}
```

### 2. Implementera default-variabelnamn

Lägg till i `shared/src/utils.ts`:

```typescript
export function getDefaultVariableName(stepType: string, stepIndex?: number): string {
  const suffix = stepIndex !== undefined ? `_${stepIndex + 1}` : '';

  switch (stepType) {
    case 'myExtract':
      return `myExtractedData${suffix}`;
    // ... andra steg
  }
}
```

### 3. Implementera step executor med stepIndex

**VIKTIGT**: Step executors MÅSTE ta emot `stepIndex` som parameter för konsistenta variabelnamn:

```typescript
export async function executeMyExtract(
  step: MyExtractStep,
  context: ExecutorContext,
  stepIndex?: number  // OBLIGATORISK parameter för variabelkonsistens
): Promise<StepExecutionResult> {
  // ... extrahera data ...

  // ANVÄND stepIndex för konsistens mellan frontend och backend
  const variableName = step.variableName || getDefaultVariableName('myExtract', stepIndex);
  variables[variableName] = extractedData;

  return {
    success: true,
    variables: {
      [variableName]: extractedData
    }
  };
}
```

### 3.1. Uppdatera Runner för att skicka stepIndex

Se till att din runner skickar `stepIndex` till step executors:

```typescript
// I din Runner-klass
async executeStep(step: RpaStep, context: RunnerContext, stepIndex?: number): Promise<StepExecutionResult> {
  switch (step.type) {
    case 'myExtract':
      return await executeMyExtract(step as MyExtractStep, executorContext, stepIndex);
    // ...
  }
}
```

### 4. Uppdatera VariableHelper

Lägg till ditt steg i listan över variabelskapande steg i `frontend/src/components/flow-editor/VariableHelper.tsx`:

```typescript
const variableCreatingSteps = [
  'extractText',
  'takeScreenshot',
  'myExtract', // Lägg till här
  // ... andra steg
];
```

### 5. Viktigt: Frontend/Backend konsistens

**KRITISKT**: Frontend och backend måste använda samma logik för variabeldetektering:

```typescript
// Både frontend och backend ska använda:
const variableName = step.variableName || getDefaultVariableName(step.type, stepIndex);
```

Detta säkerställer att:
- Variabelväljaren visar alla variabler korrekt
- Default-variabelnamn fungerar som förväntat
- Konsistent beteende mellan UI och execution

### ⚠️ Vanliga fel att undvika

1. **Glöm inte stepIndex**: Utan `stepIndex` blir variabelnamnen inkonsistenta mellan frontend och backend
   ```typescript
   // FEL - skapar inkonsistens
   const variableName = step.variableName || getDefaultVariableName('myExtract');

   // RÄTT - konsistent med frontend
   const variableName = step.variableName || getDefaultVariableName('myExtract', stepIndex);
   ```

2. **Skicka stepIndex från Runner**: Alla runners måste skicka `stepIndex` till sina step executors
   ```typescript
   // FEL - stepIndex skickas inte
   return await executeMyExtract(step, context);

   // RÄTT - stepIndex skickas med
   return await executeMyExtract(step, context, stepIndex);
   ```

3. **Använd samma logik överallt**: Frontend (VariableHelper) och backend måste använda samma `getDefaultVariableName(stepType, stepIndex)` logik

## Vanliga Misstag

### 1. Glömma uppdatera union type

Kom ihåg att lägga till din nya step-typ i `RpaStep` union type.

### 2. Felaktig kategori

Se till att step-typen hamnar i rätt kategori-fil.

### 3. Saknad runner-mappning

Kontrollera att steget mappas till rätt runner i registry.

### 4. UI-validering

Se till att editor-komponenten validerar input korrekt.

### 5. TypeScript-fel

Kör `npm run type-check` för att hitta TypeScript-fel.

## Felsökning

### Steget visas inte i toolbar

- Kontrollera att step-definitionen är exporterad
- Verifiera att den är importerad i index.ts
- Kontrollera console för JavaScript-fel

### Steget kan inte utföras

- Kontrollera att runner-metoden är implementerad
- Verifiera att steget mappas till rätt runner
- Kontrollera backend-loggar för fel

### Editor fungerar inte

- Kontrollera att editor-komponenten är registrerad
- Verifiera TypeScript-typer
- Kontrollera React-komponent för fel

## Best Practices

### 1. Namngivning

- Använd camelCase för step-typer: `myNewStep`
- Använd beskrivande namn: `extractTableData` istället för `extract`
- Var konsistent med befintliga steg

### 2. Validering

- Validera all obligatorisk input
- Ge tydliga felmeddelanden
- Använd rimliga standardvärden

### 3. Felhantering

- Fånga och logga alla fel
- Ge användarvänliga felmeddelanden
- Implementera retry-logik när lämpligt

### 4. Dokumentation

- Dokumentera alla step-properties
- Ge exempel på användning
- Förklara vad steget gör

### 5. Prestanda

- Använd rimliga timeouts
- Implementera effektiv väntan
- Undvik onödiga delays

## Nästa Steg

Efter att du lagt till ditt steg:

1. **Dokumentera** steget i API-dokumentationen
2. **Skapa exempel** för andra utvecklare
3. **Uppdatera** AI-assistant med information om steget
4. **Testa** steget i olika scenarier
5. **Dela** med teamet för feedback

## Se Också

- [Lägga till Nya Runners](./adding-new-runners.md)
- [Arkitektur Översikt](./architecture.md)
- [Kodkonventioner](./conventions.md)
- [Felsökning](./troubleshooting.md)
