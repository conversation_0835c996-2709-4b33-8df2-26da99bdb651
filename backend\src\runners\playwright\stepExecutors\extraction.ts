import { Page } from 'playwright';
import { RpaStep, ExecutionLog, getDefaultVariableName } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import fs from 'fs';
import path from 'path';

/**
 * Extraction step executors for PlaywrightRunner
 */

export interface ExtractionExecutorContext {
  page: Page;
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  flowId?: string;
}

/**
 * Execute extractText step
 */
export async function executeExtractText(
  step: RpaStep & { selector: string; variableName?: string },
  context: ExtractionExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  const interpolatedExtractTextSelector = interpolateVariables(step.selector, variables);
  const rawText = await page.textContent(interpolatedExtractTextSelector, { timeout });
  // Trim whitespace, empty lines, and spaces from beginning and end
  const text = rawText ? rawText.trim() : null;

  const variableName = step.variableName || getDefaultVariableName('extractText', stepIndex);
  variables[variableName] = text;

  onLog({
    level: 'info',
    message: `Extracted text from ${interpolatedExtractTextSelector}: ${text}`,
    stepId: step.id
  });

  return {
    success: true,
    variables: { [variableName]: text }
  };
}

/**
 * Execute extractAttribute step
 */
export async function executeExtractAttribute(
  step: RpaStep & { selector: string; attribute: string; variableName?: string },
  context: ExtractionExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  const interpolatedExtractAttrSelector = interpolateVariables(step.selector, variables);
  const interpolatedAttribute = interpolateVariables(step.attribute, variables);
  const attribute = await page.getAttribute(interpolatedExtractAttrSelector, interpolatedAttribute, { timeout });

  const variableName = step.variableName || getDefaultVariableName('extractAttribute', stepIndex);
  variables[variableName] = attribute;

  onLog({
    level: 'info',
    message: `Extracted attribute ${interpolatedAttribute} from ${interpolatedExtractAttrSelector}: ${attribute}`,
    stepId: step.id
  });

  return {
    success: true,
    variables: { [variableName]: attribute }
  };
}

/**
 * Execute takeScreenshot step
 */
export async function executeTakeScreenshot(
  step: RpaStep & { variableName?: string },
  context: ExtractionExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { page, variables, onLog, flowId } = context;

  // Create screenshots directory if it doesn't exist
  const screenshotsDir = path.join(process.cwd(), 'screenshots');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
  }

  // Generate filename with flow ID and timestamp
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `${flowId || 'flow'}-${timestamp}.png`;
  const filepath = path.join(screenshotsDir, filename);

  // Take screenshot
  await page.screenshot({ path: filepath, fullPage: true });

  // Convert to base64 for variable storage
  const screenshotBuffer = fs.readFileSync(filepath);
  const base64Screenshot = screenshotBuffer.toString('base64');

  // Store in variable if specified
  const variableName = step.variableName || getDefaultVariableName('takeScreenshot', stepIndex);
  variables[variableName] = base64Screenshot;

  onLog({
    level: 'info',
    message: `Screenshot saved: ${filename}`,
    stepId: step.id
  });

  return { 
    success: true, 
    variables: { [variableName]: base64Screenshot } 
  };
}

/**
 * Execute downloadFile step
 */
export async function executeDownloadFile(
  step: RpaStep & { triggerSelector?: string; variableName?: string; saveToFile?: boolean },
  context: ExtractionExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  // Check if triggerSelector is provided
  if (!step.triggerSelector) {
    throw new Error('triggerSelector is required for downloadFile step');
  }

  // Create downloads directory if it doesn't exist
  const downloadsDir = path.join(process.cwd(), 'downloads');
  if (!fs.existsSync(downloadsDir)) {
    fs.mkdirSync(downloadsDir, { recursive: true });
  }

  const interpolatedSelector = interpolateVariables(step.triggerSelector, variables);
  const saveToFile = step.saveToFile !== false; // Default to true for backward compatibility

  // Force download behavior for links that might open files directly
  await page.evaluate(`
    (function(selector) {
      const element = document.querySelector(selector);
      if (element && element.tagName.toLowerCase() === 'a') {
        // Add download attribute to force download instead of opening in browser
        element.setAttribute('download', '');

        // If there's a filename specified, use it as download filename
        const href = element.getAttribute('href');
        if (href && !element.getAttribute('download')) {
          const urlPath = new URL(href, window.location.href).pathname;
          const filename = urlPath.split('/').pop() || 'download';
          element.setAttribute('download', filename);
        }
      }
    })('${interpolatedSelector}')
  `);

  // Set up download handling
  const downloadPromise = page.waitForEvent('download', { timeout });

  // Click the element to trigger download
  await page.click(interpolatedSelector, { timeout });

  // Wait for download to complete
  const download = await downloadPromise;
  const filename = download.suggestedFilename();

  let base64Content = '';
  let filepath = '';

  if (saveToFile) {
    // Save to file
    filepath = path.join(downloadsDir, filename);
    await download.saveAs(filepath);

    // Read file and convert to base64
    const fileBuffer = fs.readFileSync(filepath);
    base64Content = fileBuffer.toString('base64');
  } else {
    // Get content directly without saving to disk
    const buffer = await download.createReadStream();
    const chunks: Buffer[] = [];
    
    for await (const chunk of buffer) {
      chunks.push(chunk);
    }
    
    const fileBuffer = Buffer.concat(chunks);
    base64Content = fileBuffer.toString('base64');
  }

  // Store in variables
  const variableName = step.variableName || getDefaultVariableName('downloadFile', stepIndex);
  const fileVariables = {
    [variableName]: base64Content,
    [`${variableName}_filename`]: filename,
    [`${variableName}_filepath`]: filepath
  };

  Object.assign(variables, fileVariables);

  onLog({
    level: 'info',
    message: `Downloaded file: ${filename}${saveToFile ? ` to ${filepath}` : ' (in memory only)'}`,
    stepId: step.id,
    data: {
      filename,
      filepath,
      base64Length: base64Content.length,
      base64Preview: base64Content.substring(0, 100) + (base64Content.length > 100 ? '...' : ''),
      variableName,
      variableKeys: Object.keys(fileVariables)
    }
  });

  return {
    success: true,
    variables: fileVariables
  };
}
