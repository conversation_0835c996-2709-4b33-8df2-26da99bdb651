import {
  Customer,
  CustomerToken,
  CreateCustomerRequest,
  UpdateCustomerRequest,
  CreateCustomerTokenRequest,
  UpdateCustomerTokenRequest,
  OAuth2Provider,
  generateId
} from '@rpa-project/shared';
import { statements } from '../database/database';
import { encrypt, decrypt } from '../utils/encryption';

// SQLite-based customer storage
class CustomerStorage {
  async save(customer: Customer): Promise<Customer> {
    const customerData = {
      id: customer.id,
      customer_number: customer.customerNumber,
      name: customer.name,
      visma_number: customer.vismaNumber || '',
      created_at: customer.createdAt.toISOString(),
      updated_at: customer.updatedAt.toISOString()
    };

    try {
      statements.insertCustomer.run(
        customerData.id,
        customerData.customer_number,
        customerData.name,
        customerData.visma_number,
        customerData.created_at,
        customerData.updated_at
      );
      return customer;
    } catch (error: any) {
      if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        throw new Error('Customer number already exists');
      }
      throw error;
    }
  }

  async update(id: string, customer: Partial<Customer>): Promise<Customer | null> {
    const existing = this.getById(id);
    if (!existing) return null;

    const updatedCustomer = { ...existing, ...customer, updatedAt: new Date() };

    try {
      statements.updateCustomer.run(
        updatedCustomer.customerNumber,
        updatedCustomer.name,
        updatedCustomer.vismaNumber || '',
        updatedCustomer.updatedAt.toISOString(),
        id
      );
      return updatedCustomer;
    } catch (error: any) {
      if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        throw new Error('Customer number already exists');
      }
      throw error;
    }
  }

  getById(id: string): Customer | null {
    const row = statements.getCustomerById.get(id) as any;
    if (!row) return null;

    return {
      id: row.id,
      customerNumber: row.customer_number,
      name: row.name,
      vismaNumber: row.visma_number || undefined,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  getAll(): Customer[] {
    const rows = statements.getAllCustomers.all() as any[];
    return rows.map(row => ({
      id: row.id,
      customerNumber: row.customer_number,
      name: row.name,
      vismaNumber: row.visma_number || undefined,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    }));
  }

  getByCustomerNumber(customerNumber: string): Customer | null {
    const row = statements.getCustomerByNumber.get(customerNumber) as any;
    if (!row) return null;

    return {
      id: row.id,
      customerNumber: row.customer_number,
      name: row.name,
      vismaNumber: row.visma_number || undefined,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  delete(id: string): boolean {
    const result = statements.deleteCustomer.run(id);
    return result.changes > 0;
  }

  exists(id: string): boolean {
    const result = statements.customerExists.get(id);
    return !!result;
  }

  customerNumberExists(customerNumber: string, excludeId?: string): boolean {
    const result = statements.customerNumberExists.get(customerNumber, excludeId || '');
    return !!result;
  }
}

const storage = new CustomerStorage();

export class CustomerService {
  async createCustomer(request: CreateCustomerRequest): Promise<Customer> {
    // Check if customer number already exists
    if (storage.customerNumberExists(request.customerNumber)) {
      throw new Error('Customer number already exists');
    }

    const id = generateId();
    const now = new Date();

    const customer: Customer = {
      id,
      customerNumber: request.customerNumber,
      name: request.name,
      vismaNumber: request.vismaNumber,
      createdAt: now,
      updatedAt: now
    };

    return await storage.save(customer);
  }

  async updateCustomer(id: string, request: UpdateCustomerRequest): Promise<Customer> {
    const existing = storage.getById(id);
    if (!existing) {
      throw new Error('Customer not found');
    }

    // Check if customer number already exists (excluding current customer)
    if (request.customerNumber && storage.customerNumberExists(request.customerNumber, id)) {
      throw new Error('Customer number already exists');
    }

    const updatedCustomer = await storage.update(id, request);
    if (!updatedCustomer) {
      throw new Error('Customer not found');
    }

    return updatedCustomer;
  }

  async getCustomer(id: string): Promise<Customer | null> {
    return storage.getById(id);
  }

  async getAllCustomers(): Promise<Customer[]> {
    return storage.getAll();
  }

  async getCustomerByNumber(customerNumber: string): Promise<Customer | null> {
    return storage.getByCustomerNumber(customerNumber);
  }

  async deleteCustomer(id: string): Promise<boolean> {
    if (!storage.exists(id)) {
      throw new Error('Customer not found');
    }
    return storage.delete(id);
  }

  // Get a specific customer token by name (useful for flows that need specific integrations)
  async getCustomerTokenByName(customerId: string, tokenName: string): Promise<{ apiToken?: string; refreshToken?: string } | null> {
    const tokens = tokenStorage.getByCustomerId(customerId);
    const token = tokens.find(t => t.name === tokenName);

    if (!token) return null;

    return await this.getCustomerTokenData(token.id);
  }

  // Customer token management methods
  async createCustomerToken(customerId: string, request: CreateCustomerTokenRequest): Promise<CustomerToken> {
    // Check if customer exists
    if (!storage.exists(customerId)) {
      throw new Error('Customer not found');
    }

    // Check if token name already exists for this customer
    if (tokenStorage.nameExists(customerId, request.name)) {
      throw new Error('Token name already exists for this customer');
    }

    const id = generateId();
    const now = new Date();

    // Encrypt tokens if provided
    const encryptedApiToken = request.apiToken ? encrypt(request.apiToken) : undefined;
    const encryptedRefreshToken = request.refreshToken ? encrypt(request.refreshToken) : undefined;

    const token: CustomerToken = {
      id,
      customerId,
      name: request.name,
      description: request.description,
      provider: request.provider,
      expiresAt: request.expiresAt,
      createdAt: now,
      updatedAt: now
    };

    return await tokenStorage.save(token, encryptedApiToken, encryptedRefreshToken);
  }

  async updateCustomerToken(id: string, request: UpdateCustomerTokenRequest): Promise<CustomerToken | null> {
    const existing = tokenStorage.getById(id);
    if (!existing) return null;

    // Check if new name conflicts with existing tokens (excluding current token)
    if (request.name && tokenStorage.nameExists(existing.customerId, request.name, id)) {
      throw new Error('Token name already exists for this customer');
    }

    // Encrypt tokens if provided
    const encryptedApiToken = request.apiToken ? encrypt(request.apiToken) : undefined;
    const encryptedRefreshToken = request.refreshToken ? encrypt(request.refreshToken) : undefined;

    return await tokenStorage.update(id, request, encryptedApiToken, encryptedRefreshToken);
  }

  async getCustomerToken(id: string): Promise<CustomerToken | null> {
    return tokenStorage.getById(id);
  }

  async getCustomerTokensByCustomerId(customerId: string): Promise<CustomerToken[]> {
    return tokenStorage.getByCustomerId(customerId);
  }

  async deleteCustomerToken(id: string): Promise<boolean> {
    if (!tokenStorage.exists(id)) {
      throw new Error('Customer token not found');
    }
    return tokenStorage.delete(id);
  }

  // Get decrypted token data for a specific token
  async getCustomerTokenData(id: string): Promise<{ apiToken?: string; refreshToken?: string } | null> {
    const encryptedData = await tokenStorage.getEncryptedData(id);
    if (!encryptedData) return null;

    return {
      apiToken: encryptedData.apiToken ? decrypt(encryptedData.apiToken) : undefined,
      refreshToken: encryptedData.refreshToken ? decrypt(encryptedData.refreshToken) : undefined
    };
  }

  async getExpiringTokens(minutesFromNow: number = 60): Promise<CustomerToken[]> {
    return tokenStorage.getExpiringTokens(minutesFromNow);
  }

  // Get all tokens with decrypted data for a customer
  async getCustomerTokensWithData(customerId: string): Promise<Array<CustomerToken & { apiToken?: string; refreshToken?: string }>> {
    const tokens = tokenStorage.getByCustomerId(customerId);
    const tokensWithData = [];

    for (const token of tokens) {
      const tokenData = await this.getCustomerTokenData(token.id);
      tokensWithData.push({
        ...token,
        apiToken: tokenData?.apiToken,
        refreshToken: tokenData?.refreshToken,
        hasApiToken: !!tokenData?.apiToken,
        hasRefreshToken: !!tokenData?.refreshToken
      });
    }

    return tokensWithData;
  }
}

// SQLite-based customer token storage
class CustomerTokenStorage {
  async save(token: CustomerToken, encryptedApiToken?: string, encryptedRefreshToken?: string): Promise<CustomerToken> {
    const tokenData = {
      id: token.id,
      customer_id: token.customerId,
      name: token.name,
      description: token.description || '',
      provider: token.provider,
      encrypted_api_token: encryptedApiToken || null,
      encrypted_refresh_token: encryptedRefreshToken || null,
      expires_at: token.expiresAt ? token.expiresAt.toISOString() : null,
      created_at: token.createdAt.toISOString(),
      updated_at: token.updatedAt.toISOString()
    };

    try {
      statements.insertCustomerToken.run(
        tokenData.id,
        tokenData.customer_id,
        tokenData.name,
        tokenData.description,
        tokenData.provider,
        tokenData.encrypted_api_token,
        tokenData.encrypted_refresh_token,
        tokenData.expires_at,
        tokenData.created_at,
        tokenData.updated_at
      );
      return token;
    } catch (error: any) {
      if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        throw new Error('Token name already exists for this customer');
      }
      throw error;
    }
  }

  async update(id: string, token: Partial<CustomerToken>, encryptedApiToken?: string, encryptedRefreshToken?: string): Promise<CustomerToken | null> {
    const existing = this.getById(id);
    if (!existing) return null;

    const updatedToken = { ...existing, ...token, updatedAt: new Date() };

    try {
      statements.updateCustomerToken.run(
        updatedToken.name,
        updatedToken.description || '',
        updatedToken.provider,
        encryptedApiToken || null,
        encryptedRefreshToken || null,
        updatedToken.expiresAt ? updatedToken.expiresAt.toISOString() : null,
        updatedToken.updatedAt.toISOString(),
        id
      );
      return updatedToken;
    } catch (error: any) {
      if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        throw new Error('Token name already exists for this customer');
      }
      throw error;
    }
  }

  getById(id: string): CustomerToken | null {
    const row = statements.getCustomerTokenById.get(id);
    return row ? this.mapRowToToken(row) : null;
  }

  getByCustomerId(customerId: string): CustomerToken[] {
    const rows = statements.getCustomerTokensByCustomerId.all(customerId);
    return rows.map((row: any) => this.mapRowToToken(row));
  }

  async delete(id: string): Promise<boolean> {
    const result = statements.deleteCustomerToken.run(id);
    return result.changes > 0;
  }

  async deleteByCustomerId(customerId: string): Promise<boolean> {
    const result = statements.deleteCustomerTokensByCustomerId.run(customerId);
    return result.changes > 0;
  }

  exists(id: string): boolean {
    const result = statements.customerTokenExists.get(id);
    return !!result;
  }

  nameExists(customerId: string, name: string, excludeId?: string): boolean {
    const result = statements.customerTokenNameExists.get(customerId, name, excludeId || '');
    return !!result;
  }

  // Get encrypted token data for decryption
  async getEncryptedData(id: string): Promise<{ apiToken?: string; refreshToken?: string } | null> {
    const row = statements.getCustomerTokenById.get(id) as any;
    if (!row) return null;

    return {
      apiToken: row.encrypted_api_token,
      refreshToken: row.encrypted_refresh_token
    };
  }

  // Get tokens that are expiring within specified minutes
  getExpiringTokens(minutesFromNow: number = 60): CustomerToken[] {
    const expirationThreshold = new Date(Date.now() + minutesFromNow * 60 * 1000);

    // Query for OAuth2 tokens (not manual) that expire within the threshold and have refresh tokens
    const rows = statements.database.prepare(`
      SELECT * FROM customer_tokens
      WHERE provider != 'manual'
      AND expires_at IS NOT NULL
      AND expires_at <= ?
      AND encrypted_refresh_token IS NOT NULL
      ORDER BY expires_at ASC
    `).all(expirationThreshold.toISOString()) as any[];

    return rows.map(row => this.mapRowToToken(row));
  }

  private mapRowToToken(row: any): CustomerToken {
    const expiresAt = row.expires_at ? new Date(row.expires_at) : undefined;
    const isExpired = expiresAt ? expiresAt < new Date() : false;

    return {
      id: row.id,
      customerId: row.customer_id,
      name: row.name,
      description: row.description,
      provider: row.provider || 'manual',
      hasApiToken: !!row.encrypted_api_token,
      hasRefreshToken: !!row.encrypted_refresh_token,
      expiresAt,
      isExpired,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }
}

const tokenStorage = new CustomerTokenStorage();

// Export singleton instance
export const customerService = new CustomerService();
