import { useState, useEffect } from 'react'
import { Credential } from '@rpa-project/shared'
import { credentialApi } from '../services/api'
import { CredentialForm } from '../components/credentials/CredentialForm'

export function Credentials() {
  const [credentials, setCredentials] = useState<Credential[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showForm, setShowForm] = useState(false)
  const [editingCredential, setEditingCredential] = useState<Credential | null>(null)
  const [filter, setFilter] = useState<'all' | 'password' | '2fa'>('all')

  useEffect(() => {
    loadCredentials()
  }, [filter])

  const loadCredentials = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await credentialApi.getCredentials(filter === 'all' ? undefined : filter)
      if (response.success && response.data) {
        setCredentials(response.data)
      } else {
        setError(response.error || 'Misslyckades att ladda inloggningsuppgifter')
      }
    } catch (err) {
      setError('Misslyckades att ladda inloggningsuppgifter')
      console.error('Error loading credentials:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateCredential = () => {
    setEditingCredential(null)
    setShowForm(true)
  }

  const handleEditCredential = (credential: Credential) => {
    setEditingCredential(credential)
    setShowForm(true)
  }

  const handleDeleteCredential = async (id: string) => {
    if (!confirm('Är du säker på att du vill ta bort denna inloggningsuppgift?')) {
      return
    }

    try {
      const response = await credentialApi.deleteCredential(id)
      if (response.success) {
        await loadCredentials()
      } else {
        setError(response.error || 'Misslyckades att ta bort inloggningsuppgift')
      }
    } catch (err) {
      setError('Misslyckades att ta bort inloggningsuppgift')
      console.error('Error deleting credential:', err)
    }
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingCredential(null)
    loadCredentials()
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setEditingCredential(null)
  }



  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('sv-SE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-text">Laddar inloggningsuppgifter...</div>
      </div>
    )
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-header-content">
          <p className="dashboard-title">Inloggningsuppgifter</p>
          <p className="dashboard-subtitle">
            Hantera lösenord och 2FA-tokens för automatisering.
          </p>
        </div>
        <button
          onClick={handleCreateCredential}
          className="action-button primary"
        >
          <span>Skapa inloggningsuppgift</span>
        </button>
      </div>

      {/* Filter */}
      <div className="filter-panel">
        <span className="stat-label">Filter:</span>
        <div className="filter-panel-buttons">
          {(['all', 'password', '2fa'] as const).map((filterType) => (
            <button
              key={filterType}
              onClick={() => setFilter(filterType)}
              className={`action-button-small ${filter === filterType ? 'primary' : 'secondary'}`}
            >
              <span>
                {filterType === 'all' ? 'Alla' : filterType === '2fa' ? '2FA' : 'Lösenord'}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="error-card">
          <h3 className="error-title">Fel vid laddning av inloggningsuppgifter</h3>
          <p className="error-message">{error}</p>
        </div>
      )}

      {/* Credentials List */}
      {credentials.length === 0 ? (
        <div className="empty-state-container">
          <div className="empty-state">
            <div className="empty-state-icon">🔐</div>
            <p className="empty-state-title">Inga inloggningsuppgifter än</p>
            <p className="empty-state-subtitle">Skapa din första inloggningsuppgift för att komma igång med automatiserade inloggningar</p>
            <button
              onClick={handleCreateCredential}
              className="action-button primary"
            >
              <span>Skapa första inloggningsuppgift</span>
            </button>
          </div>
        </div>
      ) : (
        <>
          <h2 className="section-title">Dina inloggningsuppgifter</h2>
          <div className="table-container">
            <div className="activity-table">
              <table className="table">
                <thead>
                  <tr>
                    <th>Namn</th>
                    <th>Typ</th>
                    <th>Användarnamn</th>
                    <th>Skapad</th>
                    <th>Åtgärder</th>
                  </tr>
                </thead>
                <tbody>
                  {credentials.map((credential) => (
                    <tr key={credential.id}>
                      <td>
                        <div>
                          <div className="flow-name">{credential.name}</div>
                          {credential.description && (
                            <div className="flow-description-small">{credential.description}</div>
                          )}
                        </div>
                      </td>
                      <td>
                        <button className={`status-button ${credential.type === 'password' ? 'status-completed' : 'status-scheduled'}`}>
                          <span>{credential.type === '2fa' ? '2FA' : 'Lösenord'}</span>
                        </button>
                      </td>
                      <td className="secondary-text">
                        {credential.type === 'password' ? (credential as any).username : '—'}
                      </td>
                      <td className="secondary-text">
                        {formatDate(credential.createdAt)}
                      </td>
                      <td>
                        <div className="flow-actions">
                          <button
                            onClick={() => handleEditCredential(credential)}
                            className="action-button-small icon-only"
                            title="Redigera inloggningsuppgift"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                              <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                          </button>
                          <button
                            onClick={() => handleDeleteCredential(credential.id)}
                            className="action-button-small danger"
                            title="Ta bort inloggningsuppgift"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <polyline points="3,6 5,6 21,6"></polyline>
                              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                              <line x1="10" y1="11" x2="10" y2="17"></line>
                              <line x1="14" y1="11" x2="14" y2="17"></line>
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}

      {/* Form Modal */}
      {showForm && (
        <CredentialForm
          credential={editingCredential}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      )}
    </div>
  )
}
