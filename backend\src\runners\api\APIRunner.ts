import OpenAI from 'openai';
import { RpaStep, FlowSettings } from '@rpa-project/shared';
import { BaseRun<PERSON>, Runner<PERSON>ontext, StepExecutionResult } from '../base';
import { STEP_RUNNER_MAPPING } from '../registry/stepTypes';
import {
  executeFortnoxCreateVoucher,
  executeFortnoxUploadFile,
  executeFortnoxAttachFileToVoucher,
  executeFortnoxUploadAndCreateVoucher
} from './stepExecutors';

/**
 * APIRunner for handling API-based steps including Fortnox integration
 * Supports various API integrations for RPA workflows
 */
export class APIRunner extends BaseRunner {
  private openai: OpenAI;

  constructor() {
    super();

    // Initialize OpenAI client
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required for API runner');
    }

    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
  }

  async initialize(_settings: FlowSettings, _variables: Record<string, any> = {}): Promise<void> {
    // API runner doesn't need special initialization
    // Variables are handled in the context
    this.logHandler({
      level: 'info',
      message: 'APIRunner initialized with OpenAI support'
    });
  }

  getSupportedStepTypes(): string[] {
    return Object.keys(STEP_RUNNER_MAPPING).filter(
      stepType => STEP_RUNNER_MAPPING[stepType as keyof typeof STEP_RUNNER_MAPPING] === 'api'
    );
  }

  async executeStep(step: RpaStep, context: RunnerContext, stepIndex?: number): Promise<StepExecutionResult> {
    const { onLog } = context;

    try {
      // Type assertion to handle the new step type until shared package is updated
      const stepType = (step as any).type;

      onLog({
        level: 'info',
        message: `Executing API step: ${stepType}`,
        stepId: step.id
      });

      // Create executor context
      const executorContext = {
        variables: context.variables,
        onLog,
        interpolateVariables: this.interpolateVariables.bind(this),
        customerId: context.customerId,
        openai: this.openai
      };

      // Route to appropriate step executor
      switch (stepType) {
        case 'fortnoxCreateVoucher':
          return await executeFortnoxCreateVoucher(step as any, executorContext, stepIndex);

        case 'fortnoxUploadFile':
          return await executeFortnoxUploadFile(step as any, executorContext, stepIndex);

        case 'fortnoxAttachFileToVoucher':
          return await executeFortnoxAttachFileToVoucher(step as any, executorContext, stepIndex);

        case 'fortnoxUploadAndCreateVoucher':
          return await executeFortnoxUploadAndCreateVoucher(step as any, executorContext, stepIndex);

        default:
          onLog({
            level: 'error',
            message: `Unsupported API step type: ${stepType}`,
            stepId: step.id
          });
          return {
            success: false,
            error: `Unsupported API step type: ${stepType}`
          };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      onLog({
        level: 'error',
        message: `Error executing API step ${step.type}: ${errorMessage}`,
        stepId: step.id
      });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async cleanup(): Promise<void> {
    // API runner doesn't need cleanup
    this.logHandler({
      level: 'info',
      message: 'APIRunner cleanup completed'
    });
  }
}
