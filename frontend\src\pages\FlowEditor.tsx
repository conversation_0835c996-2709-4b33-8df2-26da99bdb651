import { useState, useEffect, useCallback } from 'react'
import { useParams, useNavigate, Link } from 'react-router-dom'
import { RpaFlow, createEmptyFlow, FlowExecution } from '@rpa-project/shared'
import { FlowDesigner } from '../components/flow-editor/FlowDesigner'
import { FlowSettings } from '../components/flow-editor/FlowSettings'
import { FlowInfoModal } from '../components/flow-editor/FlowInfoModal'
import { ExecutionPanel } from '../components/flow-editor/ExecutionPanel'
import { VariablesModal } from '../components/flow-editor/VariablesModal'
import { Modal } from '../components/ui/Modal'

import { flowApi, executionApi } from '../services/api'

export function FlowEditor() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const isNewFlow = !id

  const [flow, setFlow] = useState<RpaFlow | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showSettings, setShowSettings] = useState(false)

  const [showFlowInfo, setShowFlowInfo] = useState(isNewFlow)
  const [showExecutionPanel, setShowExecutionPanel] = useState(false)
  const [currentExecution, setCurrentExecution] = useState<FlowExecution | null>(null)
  const [showVariables, setShowVariables] = useState(false)


  useEffect(() => {
    if (isNewFlow) {
      setFlow(createEmptyFlow('Nytt flöde', ''))
      setLoading(false)
    } else {
      loadFlow()
    }
  }, [id, isNewFlow])

  const loadFlow = async () => {
    if (!id) return

    try {
      setLoading(true)
      const response = await flowApi.getFlow(id)
      setFlow(response.data || null)
      setError(null)
    } catch (err) {
      setError('Failed to load flow')
      console.error('Error loading flow:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveFlow = useCallback(async () => {
    if (!flow) return

    try {
      setSaving(true)
      setError(null)

      if (isNewFlow) {
        const response = await flowApi.createFlow({
          name: flow.name,
          description: flow.description,
          customerId: flow.customerId,
          steps: flow.steps,
          settings: flow.settings
        })
        setFlow(response.data || null)
        if (response.data) {
          navigate(`/flows/${response.data.id}`, { replace: true })
        }
      } else {
        const response = await flowApi.updateFlow(flow.id, {
          name: flow.name,
          description: flow.description,
          customerId: flow.customerId,
          steps: flow.steps,
          settings: flow.settings
        })
        setFlow(response.data || null)
      }
    } catch (err) {
      setError('Failed to save flow')
      console.error('Error saving flow:', err)
    } finally {
      setSaving(false)
    }
  }, [flow, isNewFlow, navigate])

  const handleExecuteFlow = async () => {
    if (!flow || isNewFlow) return

    try {
      const response = await flowApi.executeFlow(flow.id)
      if (response.success && response.data) {
        setCurrentExecution(response.data)
        setShowExecutionPanel(true)

        // Poll for execution updates
        const pollExecution = async () => {
          try {
            const execResponse = await executionApi.getExecution(response.data!.id)
            if (execResponse.success && execResponse.data) {
              setCurrentExecution(execResponse.data)

              // Continue polling if still running
              if (execResponse.data.status === 'running' || execResponse.data.status === 'pending') {
                setTimeout(pollExecution, 2000)
              }
            }
          } catch (error) {
            console.error('Error polling execution:', error)
          }
        }

        // Start polling after a short delay
        setTimeout(pollExecution, 1000)
      }
    } catch (err) {
      setError('Failed to execute flow')
      console.error('Error executing flow:', err)
    }
  }

  const handleShowLatestExecution = async () => {
    if (!flow) return

    try {
      // Get the latest execution for this flow
      const response = await executionApi.getExecutions({
        flowId: flow.id,
        limit: 1
      })

      if (response.success && response.data && response.data.length > 0) {
        const latestExecution = response.data[0]
        setCurrentExecution(latestExecution)
        setShowExecutionPanel(true)
      } else {
        alert('Inga körningar hittades för detta flöde')
      }
    } catch (error) {
      console.error('Error fetching latest execution:', error)
      alert('Misslyckades att hämta senaste körning')
    }
  }



  const handleShowVariables = async () => {
    setShowVariables(true)

    // Automatically fetch latest execution when opening variables modal
    if (flow && !currentExecution) {
      try {
        const response = await executionApi.getExecutions({
          flowId: flow.id,
          limit: 1
        })

        if (response.success && response.data && response.data.length > 0) {
          const latestExecution = response.data[0]
          setCurrentExecution(latestExecution)
          console.log('Latest execution loaded for variables:', latestExecution)
        }
      } catch (error) {
        console.error('Error fetching latest execution for variables:', error)
      }
    }
  }

  const handleFlowChange = useCallback((updatedFlow: RpaFlow) => {
    setFlow(updatedFlow)
  }, [])

  if (loading) {
    return (
      <div className="page-container">
        <div className="loading-container">
          <div className="empty-state">
            <div className="empty-state-icon">🔄</div>
            <p className="empty-state-title">Loading Flow...</p>
            <p className="empty-state-subtitle">Please wait while we load your automation flow</p>
          </div>
        </div>
      </div>
    )
  }

  if (!flow) {
    return (
      <div className="page-container">
        <div className="loading-container">
          <div className="error-card">
            <h3 className="error-title">
              <span>❌</span>
              Flow Not Found
            </h3>
            <p className="error-message">The requested flow could not be found.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header" style={{ alignItems: 'center' }}>
        {/* Left side - Back button, Flow name, separator, and description */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', flex: 1, minWidth: 0 }}>
          <Link to="/flows" className="action-button secondary">
            Tillbaka
          </Link>

          <input
            type="text"
            value={flow.name}
            onChange={(e) => setFlow({ ...flow, name: e.target.value })}
            className="dashboard-title"
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              outline: 'none',
              width: `${Math.max(flow.name.length * 1.05, 5)}ch`,
              minWidth: '5ch',
              flexShrink: 0
            }}
            placeholder="Flow name"
          />

          <div style={{ width: '1px', height: '1.5rem', backgroundColor: '#e5e7eb', flexShrink: 0 }}></div>

          <input
            type="text"
            value={flow.description || ''}
            onChange={(e) => setFlow({ ...flow, description: e.target.value })}
            className="dashboard-subtitle"
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              outline: 'none',
              flex: 1,
              minWidth: '150px'
            }}
            placeholder="Flow description"
          />
        </div>

        {/* Right side - Steps and Action buttons */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', flexShrink: 0 }}>
          <span className="dashboard-subtitle" style={{ marginRight: '1rem' }}>
            {flow.steps.length} steg
          </span>

          <button
            onClick={() => setShowSettings(!showSettings)}
            className="action-button-small icon-only"
            title="Settings"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256"><rect width="256" height="256" fill="none"/><circle cx="128" cy="128" r="40" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="16"/><path d="M41.43,178.09A99.14,99.14,0,0,1,31.36,153.8l16.78-21a81.59,81.59,0,0,1,0-9.64l-16.77-21a99.43,99.43,0,0,1,10.05-24.3l26.71-3a81,81,0,0,1,6.81-6.81l3-26.7A99.14,99.14,0,0,1,102.2,31.36l21,16.78a81.59,81.59,0,0,1,9.64,0l21-16.77a99.43,99.43,0,0,1,24.3,10.05l3,26.71a81,81,0,0,1,6.81,6.81l26.7,3a99.14,99.14,0,0,1,10.07,24.29l-16.78,21a81.59,81.59,0,0,1,0,9.64l16.77,21a99.43,99.43,0,0,1-10,24.3l-26.71,3a81,81,0,0,1-6.81,6.81l-3,26.7a99.14,99.14,0,0,1-24.29,10.07l-21-16.78a81.59,81.59,0,0,1-9.64,0l-21,16.77a99.43,99.43,0,0,1-24.3-10l-3-26.71a81,81,0,0,1-6.81-6.81Z" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="16"/></svg>
          </button>

          <button
            onClick={handleShowVariables}
            className="action-button-small icon-only"
            title="Show Variables"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
              <path d="M2 17l10 5 10-5"></path>
              <path d="M2 12l10 5 10-5"></path>
            </svg>
          </button>

          {!isNewFlow && (
            <>
              <button
                onClick={handleShowLatestExecution}
                className="action-button-small icon-only"
                title="Show Latest Execution"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 256 256">
                  <path d="M224,128a8,8,0,0,1-8,8H128a8,8,0,0,1,0-16h88A8,8,0,0,1,224,128ZM128,72h88a8,8,0,0,0,0-16H128a8,8,0,0,0,0,16Zm88,112H128a8,8,0,0,0,0,16h88a8,8,0,0,0,0-16ZM82.34,42.34,56,68.69,45.66,58.34A8,8,0,0,0,34.34,69.66l16,16a8,8,0,0,0,11.32,0l32-32A8,8,0,0,0,82.34,42.34Zm0,64L56,132.69,45.66,122.34a8,8,0,0,0-11.32,11.32l16,16a8,8,0,0,0,11.32,0l32-32a8,8,0,0,0-11.32-11.32Zm0,64L56,196.69,45.66,186.34a8,8,0,0,0-11.32,11.32l16,16a8,8,0,0,0,11.32,0l32-32a8,8,0,0,0-11.32-11.32Z"></path>
                </svg>
              </button>
              <button
                onClick={handleExecuteFlow}
                className="action-button-small icon-only"
                title="Run Flow"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polygon points="5,3 19,12 5,21"></polygon>
                </svg>
              </button>
            </>
          )}

          <button
            onClick={handleSaveFlow}
            disabled={saving}
            className="action-button-small primary"
            style={{
              opacity: saving ? 0.5 : 1,
              cursor: saving ? 'not-allowed' : 'pointer'
            }}
            title="Save Flow"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
              <polyline points="17,21 17,13 7,13 7,21"></polyline>
              <polyline points="7,3 7,8 15,8"></polyline>
            </svg>
          </button>
        </div>

        {/* Error Display in Header */}
        {error && (
          <div style={{
            backgroundColor: '#fef2f2',
            border: '1px solid #fecaca',
            borderRadius: '0.375rem',
            padding: '0.5rem 1rem',
            color: '#dc2626',
            fontSize: '0.875rem',
            marginLeft: '1rem'
          }}>
            ❌ {error}
          </div>
        )}
      </div>

      {/* Flow Info Modal */}
      <FlowInfoModal
        isOpen={showFlowInfo}
        onClose={() => setShowFlowInfo(false)}
        flow={{ name: flow.name, description: flow.description, customerId: flow.customerId }}
        onFlowChange={(updates) => setFlow({ ...flow, ...updates })}
        onSave={isNewFlow ? handleSaveFlow : undefined}
      />

      {/* Settings Modal */}
      <Modal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        title="⚙️ Flow Settings"
        size="xl"
      >
        <FlowSettings
          settings={flow.settings || {}}
          onChange={(settings) => setFlow({ ...flow, settings })}
          onClose={() => setShowSettings(false)}
          flow={{ name: flow.name, description: flow.description }}
          onFlowChange={(updates) => setFlow({ ...flow, ...updates })}
          onSave={async () => {
            await handleSaveFlow()
            setShowSettings(false)
          }}
          saving={saving}
        />
      </Modal>

      {/* Variables Modal */}
      <VariablesModal
        isOpen={showVariables}
        onClose={() => setShowVariables(false)}
        flow={flow}
        currentExecution={currentExecution}
      />


      {/* Flow Designer */}
      <div style={{ height: 'calc(100vh - 120px)', overflow: 'hidden' }}>
        <FlowDesigner
          flow={flow}
          onChange={handleFlowChange}
          showAIPanel={true}
        />
      </div>

      {/* Execution Panel */}
      <ExecutionPanel
        isOpen={showExecutionPanel}
        onClose={() => {
          setShowExecutionPanel(false)
          setCurrentExecution(null)
        }}
        execution={currentExecution}
      />
    </div>
  )
}
