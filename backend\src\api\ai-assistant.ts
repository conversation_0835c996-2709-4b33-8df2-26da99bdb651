import { Router, Request, Response } from 'express';
import OpenAI from 'openai';
import Jo<PERSON> from 'joi';
import { 
  RpaFlow, 
  RpaStep, 
  ApiResponse,
  generateId,
  createEmptyFlow,
  validateFlow,
  validateStep
} from '@rpa-project/shared';
import { asyncHandler, createError } from '../middleware/errorHandler';

const router = Router();

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Validation schemas
const generateFlowSchema = Joi.object({
  prompt: Joi.string().required().min(10).max(2000),
  flowName: Joi.string().optional().max(100),
  description: Joi.string().optional().max(500)
});

const suggestNextStepSchema = Joi.object({
  flowId: Joi.string().optional(),
  currentSteps: Joi.array().items(Joi.object()).required(),
  prompt: Joi.string().required().min(5).max(1000)
});

const optimizeFlowSchema = Joi.object({
  flowId: Joi.string().required(),
  currentSteps: Joi.array().items(Joi.object()).required(),
  optimizationGoal: Joi.string().optional().valid('speed', 'reliability', 'maintainability', 'general')
});

const debugFlowSchema = Joi.object({
  flowId: Joi.string().required(),
  currentSteps: Joi.array().items(Joi.object()).required(),
  errorDescription: Joi.string().required().min(10).max(1000),
  executionLogs: Joi.array().items(Joi.object()).optional()
});

const editStepSchema = Joi.object({
  step: Joi.object().required(),
  prompt: Joi.string().required().min(5).max(1000),
  context: Joi.array().items(Joi.object()).optional()
});

// System prompt for RPA flow generation
const SYSTEM_PROMPT = `# RPA-FLÖDESASSISTENT - SYSTEMPROMPT

## ÖVERSIKT
Du är en expert på RPA (Robotic Process Automation) som hjälper användare skapa automatiseringsflöden. Din uppgift är att generera, optimera och felsöka RPA-steg baserat på användarens instruktioner.

## NODTYPER OCH FUNKTIONER

### NAVIGATION
- **navigate**: Navigera till en URL
  - \`url\`: URL att navigera till
  - \`waitUntil\`: 'load'|'domcontentloaded'|'networkidle'
  - \`timeout\`: Tid i ms innan timeout (standard: 30000)

- **goBack**: Gå tillbaka i webbläsarhistoriken
  - \`timeout\`: Tid i ms innan timeout

- **goForward**: Gå framåt i webbläsarhistoriken
  - \`timeout\`: Tid i ms innan timeout

- **reload**: Ladda om aktuell sida
  - \`timeout\`: Tid i ms innan timeout

### INTERAKTION
- **click**: Klicka på ett element
  - \`selector\`: Element-selektor
  - \`button\`: 'left'|'right'|'middle'
  - \`force\`: Boolean, tvinga klick även om elementet inte är synligt
  - \`timeout\`: Tid i ms innan timeout

- **fill**: Fyll i ett textfält
  - \`selector\`: Element-selektor
  - \`value\`: Text att fylla i
  - \`force\`: Boolean, tvinga ifyllnad även om elementet inte är synligt
  - \`timeout\`: Tid i ms innan timeout

- **fillPassword**: Fyll i lösenordsfält (säkrare)
  - \`selector\`: Element-selektor
  - \`credentialId\`: ID för lagrad credential
  - \`timeout\`: Tid i ms innan timeout

- **fill2FA**: Fyll i tvåfaktorsautentisering (2FA) kod
  - \`selector\`: Element-selektor
  - \`credentialId\`: ID för lagrad 2FA-nyckel
  - \`force\`: Boolean, tvinga ifyllnad även om elementet inte är synligt
  - \`timeout\`: Tid i ms innan timeout

- **type**: Skriv text tecken för tecken
  - \`selector\`: Element-selektor
  - \`text\`: Text att skriva
  - \`delay\`: Fördröjning mellan tangenttryckningar i ms
  - \`timeout\`: Tid i ms innan timeout

- **selectOption**: Välj alternativ i dropdown
  - \`selector\`: Element-selektor
  - \`value\`: Värde att välja
  - \`label\`: Text-etikett att välja
  - \`index\`: Index att välja
  - \`timeout\`: Tid i ms innan timeout

- **check**: Markera en checkbox
  - \`selector\`: Element-selektor
  - \`force\`: Boolean, tvinga markering även om elementet inte är synligt
  - \`timeout\`: Tid i ms innan timeout

- **uncheck**: Avmarkera en checkbox
  - \`selector\`: Element-selektor
  - \`force\`: Boolean, tvinga avmarkering även om elementet inte är synligt
  - \`timeout\`: Tid i ms innan timeout

### VÄNTAN
- **waitForSelector**: Vänta på att ett element finns/syns
  - \`selector\`: Element-selektor
  - \`state\`: 'attached'|'detached'|'visible'|'hidden'
  - \`timeout\`: Tid i ms innan timeout

- **waitForTimeout**: Vänta en specifik tid
  - \`duration\`: Tid att vänta i ms
  - \`description\`: Beskrivning av varför vi väntar

### EXTRAKTION
- **extractText**: Extrahera text från element
  - \`selector\`: Element-selektor
  - \`variableName\`: Variabelnamn att spara texten i
  - \`timeout\`: Tid i ms innan timeout

- **extractAttribute**: Extrahera attributvärde från element
  - \`selector\`: Element-selektor
  - \`attribute\`: Attributnamn att extrahera
  - \`variableName\`: Variabelnamn att spara värdet i
  - \`timeout\`: Tid i ms innan timeout

### DOKUMENTATION
- **takeScreenshot**: Ta skärmdump
  - \`name\`: Filnamn för skärmdumpen
  - \`fullPage\`: Boolean, ta skärmdump av hela sidan
  - \`path\`: Sökväg där skärmdumpen ska sparas

- **downloadFile**: Ladda ner fil från webbsida
  - \`triggerSelector\`: Element-selektor för att utlösa nedladdning (valfritt)
  - \`filename\`: Anpassat filnamn (valfritt, använder föreslaget namn annars)
  - \`variableName\`: Variabelnamn för att spara base64-innehåll (valfritt)
  - \`saveToFile\`: Boolean, spara fil till disk (standard: false)
  - \`forceDownload\`: Boolean, tvinga nedladdning istället för att öppna i webbläsare
  - \`timeout\`: Tid i ms innan timeout

### AI-BEARBETNING
- **extractPdfValues**: Extrahera specifika värden från PDF med AI och JSON-svar
  - \`base64Input\`: Base64-data för PDF eller variabelreferens (OBLIGATORISKT)
  - \`prompt\`: Instruktion för AI om vilka värden som ska extraheras (OBLIGATORISKT)
  - \`timeout\`: Tid i ms innan timeout (standard: 30000)
  - Skapar automatiskt variabler för varje värde i JSON-svaret (var-namn, var-telefon, etc.)

### VILLKORLIG LOGIK
- **conditionalClick**: Klicka endast om ett villkor uppfylls
  - \`selector\`: Element-selektor (OBLIGATORISKT)
  - \`condition\`: Typ av villkor (OBLIGATORISKT) - måste vara en av: 'exists', 'enabled', 'disabled'
  - \`clickIfTrue\`: Boolean, klicka om villkoret är sant (standard: true)
  - \`button\`: 'left'|'right'|'middle' (standard: 'left')
  - \`force\`: Boolean, tvinga klick även om elementet inte är synligt
  - \`timeout\`: Tid i ms innan timeout (standard: 30000)

### FORTNOX API-INTEGRATION
- **fortnoxCreateVoucher**: Skapa verifikation i Fortnox med AI
  - \`inputVariable\`: Variabel med data för verifikationen (OBLIGATORISKT)
  - \`aiPrompt\`: AI-instruktion för verifikationsskapande (OBLIGATORISKT)
  - \`description\`: Beskrivning av verifikationen (valfritt)
  - \`voucherSeries\`: Verifikationsserie A-Z (standard: 'A')
  - \`transactionDate\`: Transaktionsdatum YYYY-MM-DD eller variabel (standard: idag)
  - \`fileIds\`: Array med fil-ID:n att koppla till verifikationen (valfritt)
  - \`variableName\`: Variabelnamn för resultat (standard: 'var-fortnox-voucher')

- **fortnoxUploadFile**: Ladda upp fil till Fortnox arkiv
  - \`inputVariable\`: Variabel med base64-filinnehåll (OBLIGATORISKT)
  - \`filename\`: Anpassat filnamn (valfritt, använder {inputVariable}_filename annars)
  - \`description\`: Beskrivning av filen (valfritt)
  - \`variableName\`: Variabelnamn för fil-resultat (standard: 'var-fortnox-file')

- **fortnoxAttachFileToVoucher**: Koppla fil till befintlig verifikation
  - \`fileIdVariable\`: Variabel med fil-ID från uppladdning (OBLIGATORISKT)
  - \`voucherNumberVariable\`: Variabel med verifikationsnummer (OBLIGATORISKT)
  - \`voucherSeriesVariable\`: Variabel med verifikationsserie (valfritt)
  - \`variableName\`: Variabelnamn för kopplingsresultat (standard: 'var-fortnox-attachment')

- **fortnoxUploadAndCreateVoucher**: Kombinerat steg - ladda upp fil och skapa verifikation
  - \`fileInputVariable\`: Variabel med base64-filinnehåll (OBLIGATORISKT)
  - \`filename\`: Anpassat filnamn (valfritt)
  - \`fileDescription\`: Beskrivning av filen (valfritt)
  - \`voucherInputVariable\`: Variabel med data för verifikationen (OBLIGATORISKT)
  - \`aiPrompt\`: AI-instruktion för verifikationsskapande (OBLIGATORISKT)
  - \`voucherDescription\`: Beskrivning av verifikationen (valfritt)
  - \`voucherSeries\`: Verifikationsserie A-Z (standard: 'A')
  - \`transactionDate\`: Transaktionsdatum YYYY-MM-DD eller variabel (standard: idag)
  - \`variableName\`: Variabelnamn för kombinerat resultat (standard: 'var-fortnox-voucher-with-file')

## ELEMENT SELECTORS
När du väljer element är det viktigt att använda rätt selektor-strategi i prioritetsordning:

1. **Element ID** (mest pålitlig)
   - Syntax: \`#login-button\`
   - Exempel: \`{ "selector": "#username" }\`
   - Använd när elementet har ett unikt ID-attribut

2. **Text Content** (användarvänlig)
   - Syntax: \`text="Logga in"\`
   - Exempel: \`{ "selector": "text=Acceptera cookies" }\`
   - Använd när elementet har synlig text som är unik

3. **CSS Selectors** (flexibel)
   - Syntax: \`.class-name\`, \`div.container > button\`
   - Exempel: \`{ "selector": "form.login-form input[type='email']" }\`
   - Använd för mer komplexa selektioner

4. **XPath** (kraftfull men komplex)
   - Syntax: \`//tagname[@attribute='value']\`
   - Exempel: \`{ "selector": "//button[contains(text(), 'Nästa')]" }\`
   - Använd endast när andra metoder inte fungerar

5. **Kombinerade selektorer**
   - Exempel: \`{ "selector": ".form-group >> input[type='password']" }\`
   - Använd för att navigera genom komplexa DOM-strukturer

## VIKTIGA REGLER
- Varje steg MÅSTE ha ett unikt \`id\` och \`type\`-fält
- Använd alltid den mest specifika och robusta selektorn tillgänglig
- Lägg till beskrivande \`description\` för varje steg
- För lösenord, använd alltid \`credentialId\` istället för faktiska värden
- Viktigt!! Ramverket har inbyggd wait och delay - lägg aldrig till extra waitForSelector eller waitForTimeout!!
- Generera alltid kompletta JSON-objekt för varje steg

## EXEMPEL PÅ KORREKT FORMAT
\`\`\`json
[
  {
    "id": "step_1",
    "type": "navigate",
    "url": "https://example.com",
    "timeout": 30000,
    "description": "Navigera till exempel-sidan"
  },
  {
    "id": "step_2",
    "type": "waitForSelector",
    "selector": "#login-form",
    "state": "visible",
    "timeout": 30000,
    "description": "Vänta på att inloggningsformuläret laddas"
  },
  {
    "id": "step_3",
    "type": "fill",
    "selector": "input[name='username']",
    "value": "exampleUser",
    "timeout": 30000,
    "description": "Fyll i användarnamn"
  },
  {
    "id": "step_4",
    "type": "extractPdfValues",
    "base64Input": "\${var-downloaded-pdf}",
    "prompt": "Extrahera namn, telefonnummer och email från dokumentet",
    "timeout": 30000,
    "description": "Extrahera värden från PDF-dokument"
  },
  {
    "id": "step_5",
    "type": "fortnoxUploadAndCreateVoucher",
    "fileInputVariable": "var-downloaded-pdf",
    "filename": "faktura-\${var-invoice-number}.pdf",
    "fileDescription": "Inköpsfaktura från leverantör",
    "voucherInputVariable": "var-invoice-data",
    "aiPrompt": "Skapa en inköpsverifikation med moms. Använd konto 4010 för inköp, 2640 för ingående moms och 2440 för leverantörsskuld.",
    "voucherDescription": "Inköpsfaktura \${var-invoice-number}",
    "voucherSeries": "A",
    "variableName": "var-fortnox-result",
    "timeout": 60000,
    "description": "Ladda upp faktura och skapa verifikation i Fortnox"
  }
]
\`\`\`

## FORTNOX ARBETSFLÖDEN - EXEMPEL

### Enkel filuppladdning:
\`\`\`json
[
  {
    "id": "upload_1",
    "type": "fortnoxUploadFile",
    "inputVariable": "var-receipt-file",
    "filename": "kvitto-\${var-date}.pdf",
    "description": "Ladda upp kvitto till Fortnox",
    "variableName": "var-uploaded-file"
  }
]
\`\`\`

### Skapa verifikation med bifogad fil:
\`\`\`json
[
  {
    "id": "voucher_1",
    "type": "fortnoxCreateVoucher",
    "inputVariable": "var-expense-data",
    "aiPrompt": "Skapa en utgiftsverifikation. Använd konto 6250 för kostnader och 2440 för leverantörsskuld.",
    "fileIds": ["\${var-uploaded-file.fileId}"],
    "description": "Skapa verifikation med bifogad fil",
    "variableName": "var-voucher-result"
  }
]
\`\`\`

Svara ENDAST med en JSON-array av steg. Ingen extra text.`;

// Helper function to generate step ID
function generateStepId(): string {
  return generateId();
}

// Helper function to create a basic step structure
function createBaseStep(type: string, description?: string): any {
  return {
    id: generateStepId(),
    type,
    description,
    timeout: 30000
  };
}

// Helper function to parse AI response and validate steps
async function parseAndValidateSteps(aiResponse: string): Promise<RpaStep[]> {
  try {
    // Try to extract JSON from the response
    let jsonStr = aiResponse.trim();

    // Remove markdown code blocks if present
    if (jsonStr.startsWith('```json')) {
      jsonStr = jsonStr.replace(/^```json\s*/, '').replace(/\s*```$/, '');
    } else if (jsonStr.startsWith('```')) {
      jsonStr = jsonStr.replace(/^```\s*/, '').replace(/\s*```$/, '');
    }

    const parsed = JSON.parse(jsonStr);
    const steps: any[] = Array.isArray(parsed) ? parsed : [parsed];

    // Validate each step
    const validatedSteps: RpaStep[] = [];
    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];

      // Ensure step has required fields
      if (!step.id) {
        step.id = generateStepId();
      }
      if (!step.timeout) {
        step.timeout = 30000;
      }
      if (!step.type) {
        console.warn(`Step ${i} missing type field, skipping`);
        continue;
      }

      const validation = validateStep(step as RpaStep);
      if (!validation.isValid) {
        console.warn(`Invalid step generated by AI: ${validation.errors.map(e => e.message).join(', ')}`);
        continue;
      }

      validatedSteps.push(step as RpaStep);
    }

    return validatedSteps;
  } catch (error) {
    console.error('Failed to parse AI response:', error);
    throw createError('AI generated invalid response format', 500);
  }
}

// POST /api/ai-assistant/generate-flow - Generate a complete flow from description
router.post('/generate-flow', asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = generateFlowSchema.validate(req.body);
  if (error) {
    throw createError(`Validation error: ${error.details[0].message}`, 400);
  }
  
  const { prompt, flowName, description } = value;
  
  if (!process.env.OPENAI_API_KEY) {
    throw createError('OpenAI API key not configured', 500);
  }
  
  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: SYSTEM_PROMPT },
        { 
          role: 'user', 
          content: `Skapa ett komplett RPA-flöde för följande uppgift: "${prompt}"\n\nGenerera en array av RPA-steg som JSON. Inkludera alla nödvändiga steg från start till slut.` 
        }
      ],
      temperature: 0.3,
      max_tokens: 2000
    });
    
    const aiResponse = completion.choices[0]?.message?.content;
    if (!aiResponse) {
      throw createError('No response from AI', 500);
    }
    
    const steps = await parseAndValidateSteps(aiResponse);
    
    if (steps.length === 0) {
      throw createError('AI failed to generate valid steps', 500);
    }
    
    // Create flow object
    const flow: RpaFlow = {
      id: generateId(),
      name: flowName || `AI-genererat flöde ${new Date().toLocaleDateString('sv-SE')}`,
      description: description || `Automatiskt genererat från: ${prompt.substring(0, 100)}...`,
      customerId: '', // Will be set when flow is actually created
      steps,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Validate the complete flow
    const flowValidation = validateFlow(flow);
    if (!flowValidation.isValid) {
      console.warn('Generated flow has validation issues:', flowValidation.errors);
    }
    
    const response: ApiResponse<RpaFlow> = {
      success: true,
      data: flow,
      message: `Genererade ${steps.length} steg för flödet`
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error generating flow:', error);
    if (error instanceof Error && error.message.includes('API key')) {
      throw createError('OpenAI API authentication failed', 401);
    }
    throw createError('Failed to generate flow', 500);
  }
}));

// POST /api/ai-assistant/suggest-next-step - Suggest next step for existing flow
router.post('/suggest-next-step', asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = suggestNextStepSchema.validate(req.body);
  if (error) {
    throw createError(`Validation error: ${error.details[0].message}`, 400);
  }

  const { flowId, currentSteps, prompt } = value;

  if (!process.env.OPENAI_API_KEY) {
    throw createError('OpenAI API key not configured', 500);
  }

  try {
    // Create context from current steps
    const stepsContext = currentSteps.map((step: any, index: number) =>
      `${index + 1}. ${step.type}: ${JSON.stringify(step, null, 2)}`
    ).join('\n');

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: SYSTEM_PROMPT },
        {
          role: 'user',
          content: `Befintliga steg i flödet:\n${stepsContext}\n\nAnvändarens förfrågan: "${prompt}"\n\nFöreslå nästa steg eller steg som ska läggas till. Generera som JSON-array.`
        }
      ],
      temperature: 0.3,
      max_tokens: 1000
    });

    const aiResponse = completion.choices[0]?.message?.content;
    if (!aiResponse) {
      throw createError('No response from AI', 500);
    }

    const suggestedSteps = await parseAndValidateSteps(aiResponse);

    const response: ApiResponse<RpaStep[]> = {
      success: true,
      data: suggestedSteps,
      message: `Föreslår ${suggestedSteps.length} nya steg`
    };

    res.json(response);
  } catch (error) {
    console.error('Error suggesting next step:', error);
    throw createError('Failed to suggest next step', 500);
  }
}));

// POST /api/ai-assistant/optimize-flow - Optimize existing flow
router.post('/optimize-flow', asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = optimizeFlowSchema.validate(req.body);
  if (error) {
    throw createError(`Validation error: ${error.details[0].message}`, 400);
  }

  const { flowId, currentSteps, optimizationGoal = 'general' } = value;

  if (!process.env.OPENAI_API_KEY) {
    throw createError('OpenAI API key not configured', 500);
  }

  try {
    const stepsContext = currentSteps.map((step: any, index: number) =>
      `${index + 1}. ${step.type}: ${JSON.stringify(step, null, 2)}`
    ).join('\n');

    const optimizationPrompts = {
      speed: 'Optimera för snabbhet - ta bort onödiga väntetider, kombinera steg där möjligt, använd mer effektiva selektorer.',
      reliability: 'Optimera för tillförlitlighet - lägg till fler waitForSelector-steg, använd robustare selektorer, lägg till felhantering.',
      maintainability: 'Optimera för underhållbarhet - förbättra beskrivningar, använd tydligare selektorer, strukturera bättre.',
      general: 'Förbättra flödet generellt - balansera hastighet, tillförlitlighet och underhållbarhet.'
    };

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: SYSTEM_PROMPT },
        {
          role: 'user',
          content: `Nuvarande flöde:\n${stepsContext}\n\nOptimering: ${optimizationPrompts[optimizationGoal as keyof typeof optimizationPrompts]}\n\nGenerera det optimerade flödet som JSON-array med alla steg.`
        }
      ],
      temperature: 0.2,
      max_tokens: 2000
    });

    const aiResponse = completion.choices[0]?.message?.content;
    if (!aiResponse) {
      throw createError('No response from AI', 500);
    }

    const optimizedSteps = await parseAndValidateSteps(aiResponse);

    const response: ApiResponse<RpaStep[]> = {
      success: true,
      data: optimizedSteps,
      message: `Optimerade flödet för ${optimizationGoal}`
    };

    res.json(response);
  } catch (error) {
    console.error('Error optimizing flow:', error);
    throw createError('Failed to optimize flow', 500);
  }
}));

// POST /api/ai-assistant/debug-flow - Help debug flow issues
router.post('/debug-flow', asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = debugFlowSchema.validate(req.body);
  if (error) {
    throw createError(`Validation error: ${error.details[0].message}`, 400);
  }

  const { flowId, currentSteps, errorDescription, executionLogs } = value;

  if (!process.env.OPENAI_API_KEY) {
    throw createError('OpenAI API key not configured', 500);
  }

  try {
    const stepsContext = currentSteps.map((step: any, index: number) =>
      `${index + 1}. ${step.type}: ${JSON.stringify(step, null, 2)}`
    ).join('\n');

    let logsContext = '';
    if (executionLogs && executionLogs.length > 0) {
      logsContext = '\n\nExecution logs:\n' + executionLogs.map((log: any) =>
        `[${log.level}] ${log.message}`
      ).join('\n');
    }

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: SYSTEM_PROMPT + '\n\nDu hjälper också till med felsökning. Analysera problem och föreslå korrigeringar.'
        },
        {
          role: 'user',
          content: `Flöde som har problem:\n${stepsContext}\n\nFelbeskrivning: "${errorDescription}"${logsContext}\n\nAnalysera problemet och föreslå korrigerade steg som JSON-array. Förklara också vad som var fel.`
        }
      ],
      temperature: 0.2,
      max_tokens: 2000
    });

    const aiResponse = completion.choices[0]?.message?.content;
    if (!aiResponse) {
      throw createError('No response from AI', 500);
    }

    // Try to extract both explanation and steps
    let explanation = '';
    let stepsJson = aiResponse;

    // Look for explanation before JSON
    const jsonMatch = aiResponse.match(/(\[[\s\S]*\])/);
    if (jsonMatch) {
      const beforeJson = aiResponse.substring(0, jsonMatch.index);
      if (beforeJson.trim()) {
        explanation = beforeJson.trim();
      }
      stepsJson = jsonMatch[1];
    }

    const correctedSteps = await parseAndValidateSteps(stepsJson);

    const response: ApiResponse<{steps: RpaStep[], explanation: string}> = {
      success: true,
      data: {
        steps: correctedSteps,
        explanation: explanation || 'Korrigerade steg baserat på felbeskrivningen'
      },
      message: `Analyserade problemet och föreslår ${correctedSteps.length} korrigerade steg`
    };

    res.json(response);
  } catch (error) {
    console.error('Error debugging flow:', error);
    throw createError('Failed to debug flow', 500);
  }
}));

// GET /api/ai-assistant/health - Health check for AI assistant
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  const hasApiKey = !!process.env.OPENAI_API_KEY;

  let aiStatus = 'not_configured';
  if (hasApiKey) {
    try {
      // Test API connection with a minimal request
      await openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [{ role: 'user', content: 'test' }],
        max_tokens: 1
      });
      aiStatus = 'connected';
    } catch (error) {
      aiStatus = 'error';
    }
  }

  const response: ApiResponse = {
    success: true,
    data: {
      aiStatus,
      hasApiKey,
      timestamp: new Date().toISOString()
    },
    message: 'AI Assistant health check'
  };

  res.json(response);
}));

// POST /api/ai-assistant/edit-step - Edit a specific step based on prompt
router.post('/edit-step', asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = editStepSchema.validate(req.body);
  if (error) {
    throw createError(`Validation error: ${error.details[0].message}`, 400);
  }

  const { step, prompt, context = [] } = value;

  if (!process.env.OPENAI_API_KEY) {
    throw createError('OpenAI API key not configured', 500);
  }

  try {
    // Create context from surrounding steps if provided
    const contextText = context.length > 0
      ? `\n\nKontext från andra steg i flödet:\n${context.map((s: any, i: number) =>
          `${i + 1}. ${s.type}: ${JSON.stringify(s, null, 2)}`
        ).join('\n')}`
      : '';

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: SYSTEM_PROMPT },
        {
          role: 'user',
          content: `Redigera följande RPA-steg baserat på användarens instruktioner:

Nuvarande steg:
${JSON.stringify(step, null, 2)}${contextText}

Användarens instruktioner: "${prompt}"

Returnera det uppdaterade steget som ett enda JSON-objekt. Behåll samma ID och typ om inte annat specificeras.`
        }
      ],
      temperature: 0.2,
      max_tokens: 1000
    });

    const aiResponse = completion.choices[0]?.message?.content;
    if (!aiResponse) {
      throw createError('No response from AI', 500);
    }

    // Parse and validate the edited step
    const editedSteps = await parseAndValidateSteps(aiResponse);

    if (editedSteps.length !== 1) {
      throw createError('AI should return exactly one edited step', 500);
    }

    const editedStep = editedSteps[0];

    const response: ApiResponse<RpaStep> = {
      success: true,
      data: editedStep,
      message: `Redigerade steget baserat på: ${prompt.substring(0, 50)}...`
    };

    res.json(response);
  } catch (error) {
    console.error('Error editing step:', error);
    if (error instanceof Error && error.message.includes('API key')) {
      throw createError('OpenAI API authentication failed', 401);
    }
    throw createError('Failed to edit step', 500);
  }
}));

export { router as aiAssistantRoutes };

